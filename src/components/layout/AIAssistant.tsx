'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import RecommendedQuestions from '@/components/ui/RecommendedQuestions'
import OptimizedStructuredNotes from '@/components/ui/OptimizedStructuredNotes'
import DiffViewer from '@/components/ui/DiffViewer'
import { useAppStore, useActiveTab, useActiveChatMessages, useActiveStreamingNote, useActiveRecommendedQuestions } from '@/lib/store'
import { Send, Brain, Sparkles, ChevronDown, ChevronUp, Save, Settings, MessageSquare, Zap } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, updateChatMessage, setRecommendedQuestions } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const streamingNote = useActiveStreamingNote()
  const recommendedQuestions = useActiveRecommendedQuestions()
  const [chatInput, setChatInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [structuredNotesHeight, setStructuredNotesHeight] = useState(60) // 默认60%高度
  const [isStructuredNotesExpanded, setIsStructuredNotesExpanded] = useState(true) // 结构化笔记展开状态
  const [userHasManuallyControlled, setUserHasManuallyControlled] = useState(false) // 用户是否手动操作过

  // AI助手模式状态
  const [aiMode, setAiMode] = useState<'ask' | 'agent'>('ask') // 默认Ask模式
  const [pendingModifications, setPendingModifications] = useState<any[]>([]) // 待处理的修改建议

  // 使用useCallback优化回调函数
  const handleManualExpand = useCallback(() => {
    setIsStructuredNotesExpanded(true)
    setStructuredNotesHeight(80) // 展开到80%高度
    setUserHasManuallyControlled(true) // 标记用户已手动操作
  }, [])

  // 手动折叠结构化笔记
  const handleManualCollapse = useCallback(() => {
    setIsStructuredNotesExpanded(false)
    setUserHasManuallyControlled(true) // 标记用户已手动操作
  }, [])

  // 切换展开折叠状态
  const handleToggleExpanded = useCallback(() => {
    if (isStructuredNotesExpanded) {
      handleManualCollapse()
    } else {
      handleManualExpand()
    }
  }, [isStructuredNotesExpanded, handleManualExpand, handleManualCollapse])
  const [isDragging, setIsDragging] = useState(false)
  const [lastScrollTop, setLastScrollTop] = useState(0) // 记录上次滚动位置
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const dragHandleRef = useRef<HTMLDivElement>(null)

  // 使用useMemo优化计算值
  const hasChatMessages = useMemo(() => chatMessages.length > 0, [chatMessages.length])

  // 优化结构化笔记内容计算
  const structuredNoteContent = useMemo(() => {
    return streamingNote || activeTab?.aiNoteMarkdown || ''
  }, [streamingNote, activeTab?.aiNoteMarkdown])

  // 优化推荐问题显示条件
  const shouldShowRecommendedQuestions = useMemo(() => {
    return recommendedQuestions.length > 0 && !hasChatMessages
  }, [recommendedQuestions.length, hasChatMessages])

  // 使用useCallback优化生成推荐问题函数
  const generateRecommendedQuestions = useCallback(async () => {
    if (!activeTab || !activeTab.originalContent) return

    try {
      const response = await fetch('/api/generate-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: activeTab.originalContent,
          aiNote: streamingNote || activeTab.aiNoteMarkdown
        }),
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.questions) {
          setRecommendedQuestions(activeTab.id, data.questions)
        }
      }
    } catch (error) {
      console.error('生成推荐问题失败:', error)
    }
  }, [activeTab, streamingNote, setRecommendedQuestions])

  // 处理修改建议的函数
  const handleAcceptModification = useCallback((modificationId: string) => {
    // 这里可以实现具体的修改逻辑
    console.log('接受修改:', modificationId)
    // 从待处理列表中移除
    setPendingModifications(prev => prev.filter((_, index) => index.toString() !== modificationId))
  }, [])

  const handleRejectModification = useCallback((modificationId: string) => {
    console.log('拒绝修改:', modificationId)
    // 从待处理列表中移除
    setPendingModifications(prev => prev.filter((_, index) => index.toString() !== modificationId))
  }, [])

  const handleAcceptAllModifications = useCallback(() => {
    console.log('接受所有修改')
    setPendingModifications([])
  }, [])

  const handleRejectAllModifications = useCallback(() => {
    console.log('拒绝所有修改')
    setPendingModifications([])
  }, [])

  // 处理推荐问题点击 - 自动发送
  const handleQuestionClick = async (question: string) => {
    if (!activeTab || isStreaming) return

    setChatInput(question)

    // 自动发送问题
    setIsStreaming(true)

    // 如果用户没有手动操作过，且是第一次聊天，自动折叠结构化笔记
    if (chatMessages.length === 0 && !userHasManuallyControlled) {
      setIsStructuredNotesExpanded(false)
    }

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: question
      })

      // 创建AI回复的临时消息
      const assistantMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      // 准备对话历史（排除当前用户消息，因为已经添加了）
      const chatHistoryForApi = chatMessages
        .slice(0, -1) // 排除刚添加的用户消息
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }))

      // 发送请求到沉淀模式聊天API
      const response = await fetch('/api/chat-with-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: question,
          context: {
            originalContent: activeTab.originalContent || '',
            aiNote: streamingNote || activeTab.aiNoteMarkdown || '',
          },
          chatHistory: chatHistoryForApi,
          mode: aiMode // 添加模式参数
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 处理响应
      if (aiMode === 'agent') {
        // Agent模式：处理JSON响应（包含回答和修改建议）
        const data = await response.json()
        updateChatMessage(activeTab.id, assistantMessageId, data.answer || '抱歉，AI暂时无法回复，请重试。')

        // 如果有修改建议，添加到待处理列表
        if (data.noteModifications && data.noteModifications.hasModifications) {
          setPendingModifications(prev => [...prev, ...data.noteModifications.modifications])
        }
      } else {
        // Ask模式：处理流式响应
        const reader = response.body?.getReader()
        if (!reader) {
          throw new Error('无法获取响应流')
        }

        let fullResponse = ''

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = new TextDecoder().decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                if (parsed.content) {
                  fullResponse += parsed.content
                  updateChatMessage(activeTab.id, assistantMessageId, fullResponse)
                }
              } catch (e) {
                console.warn('解析JSON失败:', e)
              }
            }
          }
        }

        if (!fullResponse) {
          updateChatMessage(activeTab.id, assistantMessageId, '抱歉，AI暂时无法回复，请重试。')
        }
      }
    } catch (error) {
      console.error('聊天请求失败:', error)
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: '抱歉，发生了错误，请重试。'
      })
    } finally {
      setIsStreaming(false)
      setChatInput('')
    }
  }

  // 新内容加载时的自动行为
  useEffect(() => {
    // 当新内容加载时，自动展开结构化笔记，重置用户手动控制状态
    if (activeTab && !activeTab.aiAnalyzing && activeTab.aiNoteMarkdown) {
      setIsStructuredNotesExpanded(true)
      setUserHasManuallyControlled(false) // 重置手动控制状态
    }
  }, [activeTab?.id, activeTab?.aiNoteMarkdown])

  // 自动滚动到聊天底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [chatMessages, isStreaming])

  // 在结构化笔记生成完成后立即生成推荐问题
  useEffect(() => {
    if (activeTab && !activeTab.aiAnalyzing && activeTab.aiNoteMarkdown && recommendedQuestions.length === 0) {
      // 立即生成推荐问题，不延迟
      generateRecommendedQuestions()
    }
  }, [activeTab?.aiAnalyzing, activeTab?.aiNoteMarkdown, recommendedQuestions.length])

  // 拖拽功能实现
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const containerHeight = window.innerHeight - 200 // 减去其他UI元素的高度
      const newHeight = Math.max(10, Math.min(80, (e.clientY / containerHeight) * 100))
      setStructuredNotesHeight(newHeight)
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging])

  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  // 处理流式聊天
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab || isStreaming) return

    const input = chatInput.trim()
    setChatInput('')
    setIsStreaming(true)

    // 如果用户没有手动操作过，且是第一次聊天，自动折叠结构化笔记
    if (chatMessages.length === 0 && !userHasManuallyControlled) {
      setIsStructuredNotesExpanded(false)
    }

    try {
      // 添加用户消息
      addChatMessage(activeTab.id, {
        role: 'user',
        content: input
      })

      // 创建AI回复的临时消息
      const assistantMessageId = addChatMessage(activeTab.id, {
        role: 'assistant',
        content: ''
      })

      // 准备对话历史（排除当前用户消息，因为已经添加了）
      const chatHistoryForApi = chatMessages
        .slice(0, -1) // 排除刚添加的用户消息
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }))

      // 发送请求到沉淀模式聊天API
      const response = await fetch('/api/chat-with-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          context: {
            originalContent: activeTab.originalContent || '',
            aiNote: streamingNote || activeTab.aiNoteMarkdown || '',
          },
          chatHistory: chatHistoryForApi,
          mode: aiMode // 添加模式参数
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 处理响应
      if (aiMode === 'agent') {
        // Agent模式：处理JSON响应（包含回答和修改建议）
        const data = await response.json()
        updateChatMessage(activeTab.id, assistantMessageId, data.answer || '抱歉，AI暂时无法回复，请重试。')

        // 如果有修改建议，添加到待处理列表
        if (data.noteModifications && data.noteModifications.hasModifications) {
          setPendingModifications(prev => [...prev, ...data.noteModifications.modifications])
        }
      } else {
        // Ask模式：处理流式响应
        const reader = response.body?.getReader()
        if (!reader) {
          throw new Error('无法获取响应流')
        }

        let fullResponse = ''

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = new TextDecoder().decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                if (parsed.content) {
                  fullResponse += parsed.content
                  updateChatMessage(activeTab.id, assistantMessageId, fullResponse)
                }
              } catch (e) {
                console.warn('解析JSON失败:', e)
              }
            }
          }
        }

        if (!fullResponse) {
          updateChatMessage(activeTab.id, assistantMessageId, '抱歉，AI暂时无法回复，请重试。')
        }
      }
    } catch (error) {
      console.error('聊天请求失败:', error)
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: '抱歉，发生了错误，请重试。'
      })
    } finally {
      setIsStreaming(false)
    }
  }

  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <Brain className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
            <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col liquid-glass">
      {/* 根据聊天状态决定布局 */}
      {!hasChatMessages ? (
        /* 无聊天时：结构化笔记占满全屏，无卡片边框 */
        <div className="h-full flex flex-col p-6">
          {/* 结构化笔记内容区域 - 无边框，自由滚动 */}
          <div className="flex-1 overflow-y-auto">
            {!activeTab ? (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-lg font-semibold text-gray-900 mb-2">AI助手</p>
                    <p className="text-sm text-gray-600">选择或创建一个标签页开始使用</p>
                  </div>
                </div>
              </div>
            ) : activeTab.isLoading ? (
              <div className="h-full flex items-center justify-center">
                <ModernLoader variant="dots" size="lg" text="正在分析内容..." className="text-center" />
              </div>
            ) : (streamingNote || activeTab.aiNoteMarkdown) ? (
              /* 无聊天时：使用OptimizedStructuredNotes组件的全屏模式 */
              <OptimizedStructuredNotes
                content={activeTab?.aiNoteMarkdown || ''}
                streamingContent={streamingNote}
                isAnalyzing={activeTab?.aiAnalyzing}
                isExpanded={true}
                height={100}
                onToggleExpanded={() => {}}
                onManualExpand={() => {}}
                onManualCollapse={() => {}}
                fullscreen={true}
              />
            ) : activeTab.aiAnalyzing ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <ModernLoader variant="dots" size="lg" text="正在生成结构化笔记..." className="text-center" />
                  <p className="text-sm text-gray-500 mt-4">AI正在为您整理知识要点</p>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gray-100 rounded-2xl flex items-center justify-center">
                    <Brain className="w-8 h-8 text-gray-400" />
                  </div>
                  <div>
                    <p className="text-gray-500 text-lg font-medium">准备就绪</p>
                    <p className="text-gray-400 text-sm mt-2">开始输入内容，AI将为您生成结构化笔记</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 保存按钮 - 在无聊天时显示 */}
          {(streamingNote || activeTab.aiNoteMarkdown) && !activeTab.aiAnalyzing && (
            <div className="mb-4 flex justify-center">
              <button
                onClick={async () => {
                  if (!activeTab) return
                  try {
                    const { saveNote } = useAppStore.getState()
                    await saveNote({
                      title: activeTab.title,
                      originalContent: activeTab.originalContent,
                      structuredNotes: streamingNote || activeTab.aiNoteMarkdown || '',
                      integratedNotes: streamingNote || activeTab.aiNoteMarkdown || '', // 无聊天时直接使用结构化笔记
                      sourceType: activeTab.sourceType,
                      sourceData: activeTab.sourceData
                    })
                  } catch (error) {
                    console.error('保存笔记失败:', error)
                  }
                }}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center space-x-2"
              >
                <Save size={16} />
                <span>保存到知识库</span>
              </button>
            </div>
          )}

          {/* 推荐问题卡片 - 在无聊天时显示在输入框上方，紧贴输入框 */}
          {shouldShowRecommendedQuestions && (
            <div className="mb-2">
              <RecommendedQuestions
                questions={recommendedQuestions}
                onQuestionClick={handleQuestionClick}
              />
            </div>
          )}

          {/* AI助手模式切换 */}
          <div className="mb-3 flex justify-center">
            <div className="bg-white rounded-full border border-gray-200 p-1 shadow-lg">
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setAiMode('ask')}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                    aiMode === 'ask'
                      ? 'bg-blue-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <MessageSquare size={14} />
                  <span>Ask模式</span>
                </button>
                <button
                  onClick={() => setAiMode('agent')}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                    aiMode === 'agent'
                      ? 'bg-purple-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Zap size={14} />
                  <span>Agent模式</span>
                </button>
              </div>
            </div>
          </div>

          {/* 聊天输入框 - 圆润设计，与有聊天状态保持一致 */}
          <div className="pt-2">
            <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white rounded-full border border-gray-200 p-3 shadow-xl">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder={aiMode === 'agent' ? "问我问题，我会自动整理到笔记中..." : "对内容有疑问？问我任何问题..."}
                  disabled={isStreaming || !activeTab}
                  className="w-full px-4 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400 rounded-full"
                />
              </div>
              <button
                type="submit"
                disabled={!chatInput.trim() || isStreaming || !activeTab}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
              >
                <Send size={14} />
              </button>
            </form>
          </div>
        </div>
      ) : (
        /* 有聊天时：新的布局 - 悬浮结构化笔记卡片 + 全高度AI助手 */
        <div className="h-full relative">
          {/* AI助手对话容器 - 占用全部高度，智能滚动从结构化笔记底部开始 */}
          <div className="h-full flex flex-col">
            <div
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto space-y-6 bg-transparent transition-all duration-300 ease-in-out"
              style={{
                paddingTop: isStructuredNotesExpanded ? `calc(${structuredNotesHeight}% + 2rem)` : '5rem', // 根据展开状态调整顶部间距
                paddingLeft: '1rem',
                paddingRight: '1rem',
                paddingBottom: '6rem' // 为输入框和推荐问题留出足够空间
              }}
            >
              {chatMessages.map((message) => {
                if (!message.id || !message.content) return null
                return (
                  <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[85%] px-5 py-4 text-sm shadow-lg backdrop-blur-sm ${
                      message.role === 'user'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-3xl border border-blue-400/20'
                        : 'bg-white/95 border border-gray-200/60 text-gray-900 rounded-3xl'
                    }`}>
                      {message.role === 'assistant' ? (
                        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-gray-800 prose-p:text-gray-700 prose-ul:text-gray-700 prose-ol:text-gray-700 prose-code:text-blue-600 prose-code:bg-blue-50">
                          {message.content || '正在思考...'}
                        </SafeMarkdown>
                      ) : (
                        <span>{message.content}</span>
                      )}
                    </div>
                  </div>
                )
              }).filter(Boolean)}

              {isStreaming && (
                <div className="flex justify-start">
                  <div className="bg-white/95 border border-gray-200/60 rounded-3xl px-5 py-4 shadow-lg backdrop-blur-sm">
                    <div className="flex items-center space-x-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                      </div>
                      <span className="text-sm text-gray-500">AI正在思考...</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Agent模式下显示修改建议 */}
              {aiMode === 'agent' && pendingModifications.length > 0 && (
                <div className="px-1">
                  <DiffViewer
                    modifications={pendingModifications}
                    onAccept={handleAcceptModification}
                    onReject={handleRejectModification}
                    onAcceptAll={handleAcceptAllModifications}
                    onRejectAll={handleRejectAllModifications}
                  />
                </div>
              )}
            </div>

            {/* 推荐问题卡片 - 浮动在输入框上方，紧贴输入框，有聊天记录后隐藏 */}
            {shouldShowRecommendedQuestions && (
              <div className="absolute bottom-[4.5rem] left-4 right-4 z-50">
                <RecommendedQuestions
                  questions={recommendedQuestions}
                  onQuestionClick={handleQuestionClick}
                />
              </div>
            )}

            {/* 固定在底部的输入框 - 非透明背景 */}
            <div className="absolute bottom-0 left-0 right-0 bg-transparent">
              <div className="p-3 mx-4 mb-3 space-y-3">
                {/* AI助手模式切换 */}
                <div className="flex justify-center">
                  <div className="bg-white rounded-full border border-gray-200 p-1 shadow-lg">
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => setAiMode('ask')}
                        className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex items-center space-x-1.5 ${
                          aiMode === 'ask'
                            ? 'bg-blue-500 text-white shadow-md'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <MessageSquare size={12} />
                        <span>Ask</span>
                      </button>
                      <button
                        onClick={() => setAiMode('agent')}
                        className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex items-center space-x-1.5 ${
                          aiMode === 'agent'
                            ? 'bg-purple-500 text-white shadow-md'
                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                        }`}
                      >
                        <Zap size={12} />
                        <span>Agent</span>
                      </button>
                    </div>
                  </div>
                </div>

                <form onSubmit={handleChatSubmit} className="flex space-x-3 bg-white rounded-full border border-gray-200 p-3 shadow-xl">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      placeholder={aiMode === 'agent' ? "问我问题，我会自动整理到笔记中..." : "输入消息..."}
                      disabled={isStreaming}
                      className="w-full px-4 py-2 border-0 bg-transparent focus:ring-0 focus:outline-none text-sm placeholder-gray-500 disabled:text-gray-400 rounded-full"
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={!chatInput.trim() || isStreaming}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
                  >
                    <Send size={14} />
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* 优化的结构化笔记组件 - 只在有聊天时显示悬浮卡片 */}
          {(streamingNote || activeTab?.aiNoteMarkdown) && (
            <OptimizedStructuredNotes
              content={activeTab?.aiNoteMarkdown || ''}
              streamingContent={streamingNote}
              isAnalyzing={activeTab?.aiAnalyzing}
              isExpanded={isStructuredNotesExpanded}
              height={structuredNotesHeight}
              onToggleExpanded={handleToggleExpanded}
              onManualExpand={handleManualExpand}
              onManualCollapse={handleManualCollapse}
            />
          )}
        </div>
      )}
    </div>
  )
}

export default AIAssistant
